# Clerk Billing Setup Guide

## Overview
This guide walks you through setting up Clerk billing to replace the mock subscription system with real billing functionality.

## Prerequisites
- Clerk account with billing feature enabled
- Stripe account (or use Clerk's development gateway)
- Access to Clerk Dashboard

## Step 1: Enable Clerk Billing

### 1.1 Access Billing Settings
1. Go to [Clerk Dashboard](https://dashboard.clerk.com)
2. Navigate to **Billing Settings** → [Billing Settings](https://dashboard.clerk.com/last-active?path=billing/settings)
3. Click **Enable Billing**

### 1.2 Configure Payment Gateway
Choose one of these options:

**Option A: Clerk Development Gateway (Recommended for Testing)**
- Select "Clerk development gateway"
- This uses a shared test Stripe account
- Perfect for development and testing

**Option B: Your Own Stripe Account**
- Select "Stripe account"
- Connect your Stripe account
- Required for production

## Step 2: Create Subscription Plans

### 2.1 Navigate to Plans
1. Go to [Plans](https://dashboard.clerk.com/last-active?path=billing/plans)
2. Select **Plans for Users** tab
3. Click **Add Plan**

### 2.2 <PERSON><PERSON> "Reply Guy" Plan
```
Plan Name: reply-guy
Display Name: Reply Guy
Price: $9.00 USD
Billing Interval: Monthly
Description: Perfect for individuals getting started with social media automation
```

**Features to Add:**
- `ai_calls` - Limit: 100
- `image_generations` - Limit: 20
- `monitored_accounts` - Limit: 3
- `mentions_per_month` - Limit: 1000
- `storage_gb` - Limit: 1
- `team_members` - Limit: 1

### 2.3 Create "Reply God" Plan
```
Plan Name: reply-god
Display Name: Reply God
Price: $29.00 USD
Billing Interval: Monthly
Description: Advanced features for power users and small teams
```

**Features to Add:**
- `ai_calls` - Limit: 500
- `image_generations` - Limit: 50
- `monitored_accounts` - Limit: 10
- `mentions_per_month` - Unlimited (-1)
- `storage_gb` - Limit: 5
- `team_members` - Limit: 1

### 2.4 Create "Team Plan" Plan
```
Plan Name: team-plan
Display Name: Team Plan
Price: $99.00 USD
Billing Interval: Monthly
Description: Enterprise solution for teams and organizations
```

**Features to Add:**
- `ai_calls` - Unlimited (-1)
- `image_generations` - Limit: 100
- `monitored_accounts` - Limit: 50
- `mentions_per_month` - Unlimited (-1)
- `storage_gb` - Limit: 25
- `team_members` - Limit: 5

## Step 3: Configure Session Token Claims

### 3.1 Add Billing Claims
1. Go to [Sessions](https://dashboard.clerk.com/last-active?path=sessions)
2. Under **Customize session token**, add these claims:

```json
{
  "plan": "{{user.plan}}",
  "features": "{{user.features}}",
  "subscription_status": "{{user.subscription_status}}"
}
```

## Step 4: Update Environment Variables

Add these to your `.env` file:

```env
# Clerk Billing
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
CLERK_WEBHOOK_SIGNING_SECRET=whsec_...
CLERK_JWT_KEY=your_clerk_jwt_key
```

## Step 5: Configure Webhooks

### 5.1 Add Billing Webhooks
1. Go to [Webhooks](https://dashboard.clerk.com/last-active?path=webhooks)
2. Add your webhook endpoint: `https://yourdomain.com/api/webhooks/clerk`
3. Enable these events:
   - `subscription.created`
   - `subscription.updated`
   - `subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

## Step 6: Test the Integration

### 6.1 Test Subscription Flow
1. Visit `/pricing` page
2. Select a plan
3. Complete checkout process
4. Verify webhook events are received
5. Check user's plan is updated in database

### 6.2 Test Feature Access
1. Use the billing tRPC routes to check feature access
2. Verify usage limits are enforced
3. Test plan upgrades/downgrades

## Step 7: Migration from Legacy System

### 7.1 Migrate Existing Users
Run the migration script to move users from legacy plans to Clerk billing:

```bash
# This will be created once DB is accessible
bun run migrate:clerk-billing
```

### 7.2 Update Feature Checks
Replace legacy feature checks with Clerk billing checks:

```typescript
// Old way
const canUse = await canUserUseFeature(userId, 'AI_CALLS');

// New way
const canUse = await canUserUseClerkFeature(userId, 'AI_CALLS');
```

## Troubleshooting

### Common Issues
1. **Webhook not receiving events**: Check webhook URL and signing secret
2. **Plans not showing**: Ensure plans are marked as "Publicly available"
3. **Feature limits not working**: Verify feature names match exactly
4. **Payment failures**: Check Stripe configuration and test cards

### Debug Mode
Enable verbose logging in development:

```env
VERBOSE_LOGGING=true
ENABLE_TRPC_REQUEST_LOGS=true
```

## Production Checklist

- [ ] Stripe account connected (not development gateway)
- [ ] Webhook endpoints configured with HTTPS
- [ ] Environment variables set in production
- [ ] Plans tested with real payment methods
- [ ] Feature access controls verified
- [ ] User migration completed
- [ ] Monitoring and alerts configured

## Support

For issues with Clerk billing:
1. Check [Clerk Documentation](https://clerk.com/docs/billing)
2. Contact Clerk Support
3. Review webhook logs in Clerk Dashboard

For application-specific issues:
1. Check application logs
2. Verify database schema updates
3. Test tRPC routes in development
